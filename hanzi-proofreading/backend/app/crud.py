from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, func, text, desc, asc, case
from typing import List, Optional, Set, Dict, Any
from datetime import datetime, timedelta
from . import models, schemas

# 汉字CRUD操作
class HanziCRUD:
    
    @staticmethod
    def create(db: Session, hanzi: schemas.HanziCreate) -> models.Hanzi:
        """创建汉字"""
        db_hanzi = models.Hanzi(**hanzi.model_dump())
        db.add(db_hanzi)
        db.commit()
        db.refresh(db_hanzi)
        return db_hanzi
    
    @staticmethod
    def get_by_unicode(db: Session, unicode_code: str) -> Optional[models.Hanzi]:
        """根据Unicode编码获取汉字"""
        return db.query(models.Hanzi).filter(models.Hanzi.unicode_code == unicode_code).first()
    
    @staticmethod
    def get_by_character(db: Session, character: str) -> Optional[models.Hanzi]:
        """根据字符获取汉字"""
        return db.query(models.Hanzi).filter(models.Hanzi.character == character).first()
    
    @staticmethod
    def search(db: Session, query: str, limit: int = 20, offset: int = 0) -> List[models.Hanzi]:
        """搜索汉字（优先精确匹配，然后模糊匹配）"""
        # 如果查询是单个字符，获取其Unicode编码
        unicode_code_from_char = None
        is_single_char = False
        try:
            # 检查是否为单个字符（包括生僻字）
            if query and len(query) == 1:  # 使用字符长度而不是字节长度
                unicode_code_from_char = format(ord(query), 'X')
                is_single_char = True
        except Exception as e:
            pass
        
        # 如果是单个字符，使用Unicode编码进行精确匹配（避免字符编码问题）
        if is_single_char and unicode_code_from_char:
            # 只使用Unicode编码匹配，避免字符编码不一致的问题
            exact_conditions = [
                models.Hanzi.unicode_code == unicode_code_from_char,
                models.Hanzi.unicode_code == unicode_code_from_char.upper(),
                models.Hanzi.unicode_code == unicode_code_from_char.lower()
            ]
            
            exact_results = db.query(models.Hanzi).filter(
                or_(*exact_conditions)
            ).execution_options(compiled_cache={}).offset(offset).limit(limit).all()
            
            # 如果找到精确匹配，直接返回
            if exact_results:
                return exact_results
            
            # 如果没有找到，返回空结果
            return []
        
        # 如果是Unicode编码查询，进行精确和模糊匹配
        else:
            # 先尝试精确匹配
            exact_conditions = [
                models.Hanzi.character == query,
                models.Hanzi.unicode_code == query,
                models.Hanzi.unicode_code == query.upper(),
                models.Hanzi.unicode_code == query.lower()
            ]
            
            exact_results = db.query(models.Hanzi).filter(
                or_(*exact_conditions)
            ).execution_options(compiled_cache={}).offset(offset).limit(limit).all()
            
            # 如果精确匹配有结果，直接返回
            if exact_results:
                return exact_results
            
            # 如果精确匹配没有结果，进行模糊匹配（仅对Unicode编码查询）
            fuzzy_conditions = [
                models.Hanzi.character.like(f"%{query}%"),
                models.Hanzi.unicode_code.like(f"%{query}%"),
                models.Hanzi.unicode_code.like(f"%{query.upper()}%"),
                models.Hanzi.unicode_code.like(f"%{query.lower()}%")
            ]
            
            return db.query(models.Hanzi).filter(
                or_(*fuzzy_conditions)
            ).execution_options(compiled_cache={}).offset(offset).limit(limit).all()
    
    @staticmethod
    def get_detail(db: Session, unicode_code: str) -> Optional[models.Hanzi]:
        """获取汉字详情（包含关系和元数据）"""
        return db.query(models.Hanzi).options(
            joinedload(models.Hanzi.source_relations),
            joinedload(models.Hanzi.target_relations),
            joinedload(models.Hanzi.metadata_entries)
        ).filter(models.Hanzi.unicode_code == unicode_code).first()

# 汉字关系CRUD操作
class HanziRelationCRUD:
    
    @staticmethod
    def create(db: Session, relation: schemas.HanziRelationCreate) -> models.HanziRelation:
        """创建汉字关系"""
        db_relation = models.HanziRelation(**relation.model_dump())
        db.add(db_relation)
        db.commit()
        db.refresh(db_relation)
        return db_relation
    
    @staticmethod
    def get_relations_by_hanzi(db: Session, unicode_code: str) -> List[models.HanziRelation]:
        """获取汉字的所有关系"""
        return db.query(models.HanziRelation).filter(
            or_(
                models.HanziRelation.source_unicode == unicode_code,
                models.HanziRelation.target_unicode == unicode_code
            )
        ).all()
    
    @staticmethod
    def delete(db: Session, relation_id: int) -> bool:
        """删除关系"""
        relation = db.query(models.HanziRelation).filter(models.HanziRelation.id == relation_id).first()
        if relation:
            db.delete(relation)
            db.commit()
            return True
        return False
    
    @staticmethod
    def get_related_hanzi_unicodes(db: Session, unicode_code: str) -> List[str]:
        """获取直接关联的汉字Unicode列表"""
        relations = db.query(models.HanziRelation).filter(
            or_(
                models.HanziRelation.source_unicode == unicode_code,
                models.HanziRelation.target_unicode == unicode_code
            )
        ).all()
        
        related_unicodes = []
        for relation in relations:
            if relation.source_unicode == unicode_code:
                related_unicodes.append(relation.target_unicode)
            else:
                related_unicodes.append(relation.source_unicode)
        
        return related_unicodes

# 汉字元数据CRUD操作
class HanziMetadataCRUD:
    
    @staticmethod
    def create_or_update(db: Session, metadata: schemas.HanziMetadataCreate) -> models.HanziMetadata:
        """创建或更新汉字元数据"""
        # 查找是否已存在
        existing = db.query(models.HanziMetadata).filter(
            models.HanziMetadata.unicode_code == metadata.unicode_code
        ).first()
        
        if existing:
            # 更新现有记录
            for key, value in metadata.model_dump().items():
                setattr(existing, key, value)
            db.commit()
            db.refresh(existing)
            return existing
        else:
            # 创建新记录
            db_metadata = models.HanziMetadata(**metadata.model_dump())
            db.add(db_metadata)
            db.commit()
            db.refresh(db_metadata)
            return db_metadata
    
    @staticmethod
    def get_by_hanzi(db: Session, unicode_code: str) -> Optional[models.HanziMetadata]:
        """获取汉字的元数据（返回单个记录）"""
        return db.query(models.HanziMetadata).filter(
            models.HanziMetadata.unicode_code == unicode_code
        ).first()
    
    @staticmethod
    def update_category(db: Session, unicode_code: str, category: str, value: bool) -> bool:
        """更新特定类别的值"""
        try:
            metadata = db.query(models.HanziMetadata).filter(
                models.HanziMetadata.unicode_code == unicode_code
            ).first()
            
            if not metadata:
                # 创建新的元数据记录
                metadata_data = schemas.HanziMetadataCreate(unicode_code=unicode_code)
                metadata = models.HanziMetadata(**metadata_data.model_dump())
                db.add(metadata)
            
            # 更新对应的类别字段
            if category == "fanTiZi":
                metadata.is_fan_ti_zi = value
            elif category == "jianTiZi":
                metadata.is_jian_ti_zi = value
            elif category == "zhengTiZi":
                metadata.is_zheng_ti_zi = value
            elif category == "yiTiZi":
                metadata.is_yi_ti_zi = value
            else:
                return False
            
            db.commit()
            return True
        except Exception:
            db.rollback()
            return False

# 汉字关系网络操作
class RelationNetworkCRUD:
    
    @staticmethod
    def get_relation_group(db: Session, unicode_code: str) -> schemas.HanziRelationGroup:
        """获取汉字关系网络组"""
        visited: Set[str] = set()
        all_hanzi_unicodes: List[str] = []
        
        def dfs_relations(current_unicode: str):
            """深度优先搜索关联汉字"""
            if current_unicode in visited:
                return
            
            visited.add(current_unicode)
            all_hanzi_unicodes.append(current_unicode)
            
            # 获取直接关联的汉字Unicode
            related_unicodes = HanziRelationCRUD.get_related_hanzi_unicodes(db, current_unicode)
            
            # 递归查询每个关联汉字
            for related_unicode in related_unicodes:
                dfs_relations(related_unicode)
        
        # 从目标汉字开始递归查询
        dfs_relations(unicode_code)
        
        # 获取所有汉字信息
        related_hanzi = db.query(models.Hanzi).filter(
            models.Hanzi.unicode_code.in_(all_hanzi_unicodes)
        ).all()
        
        # 获取所有关系
        relations = db.query(models.HanziRelation).filter(
            and_(
                models.HanziRelation.source_unicode.in_(all_hanzi_unicodes),
                models.HanziRelation.target_unicode.in_(all_hanzi_unicodes)
            )
        ).all()
        
        # 按关系类型分组
        zhengyi_relations = [r for r in relations if r.relation_type == models.RelationTypeEnum.zhengyi]
        fanjian_relations = [r for r in relations if r.relation_type == models.RelationTypeEnum.fanjian]
        
        return schemas.HanziRelationGroup(
            related_hanzi=related_hanzi,
            zhengyi_relations=zhengyi_relations,
            fanjian_relations=fanjian_relations
        )
    
    @staticmethod
    def _update_metadata_from_relations(db: Session, unicode_codes: List[str]) -> bool:
        """根据关系更新汉字metadata - 只更新实际参与关系的汉字"""
        try:
            # 获取所有涉及的关系
            relations = db.query(models.HanziRelation).filter(
                or_(
                    models.HanziRelation.source_unicode.in_(unicode_codes),
                    models.HanziRelation.target_unicode.in_(unicode_codes)
                )
            ).all()

            # 找出实际参与关系的汉字（只更新这些汉字的metadata）
            hanzi_in_relations = set()
            for relation in relations:
                hanzi_in_relations.add(relation.source_unicode)
                hanzi_in_relations.add(relation.target_unicode)

            # 如果没有关系，不需要更新任何metadata
            if not hanzi_in_relations:
                return True

            # 批量获取现有的metadata记录（只获取参与关系的汉字）
            existing_metadata = db.query(models.HanziMetadata).filter(
                models.HanziMetadata.unicode_code.in_(list(hanzi_in_relations))
            ).all()

            # 创建现有metadata的字典，方便快速查找
            existing_metadata_dict = {meta.unicode_code: meta for meta in existing_metadata}

            # 为每个参与关系的汉字重新计算metadata
            for unicode_code in hanzi_in_relations:
                # 初始化所有字段为False
                is_fan_ti_zi = False
                is_jian_ti_zi = False
                is_zheng_ti_zi = False
                is_yi_ti_zi = False

                # 分析该汉字在关系中的角色
                for relation in relations:
                    if relation.relation_type == models.RelationTypeEnum.zhengyi:
                        # 正异关系：source是异体字，target是标准字
                        if relation.source_unicode == unicode_code:
                            is_yi_ti_zi = True
                        elif relation.target_unicode == unicode_code:
                            is_zheng_ti_zi = True

                    elif relation.relation_type == models.RelationTypeEnum.fanjian:
                        # 繁简关系：source是繁体字，target是简体字
                        if relation.source_unicode == unicode_code:
                            is_fan_ti_zi = True
                        elif relation.target_unicode == unicode_code:
                            is_jian_ti_zi = True

                # 更新或创建metadata（不commit）
                if unicode_code in existing_metadata_dict:
                    # 更新现有记录
                    existing = existing_metadata_dict[unicode_code]
                    existing.is_fan_ti_zi = is_fan_ti_zi
                    existing.is_jian_ti_zi = is_jian_ti_zi
                    existing.is_zheng_ti_zi = is_zheng_ti_zi
                    existing.is_yi_ti_zi = is_yi_ti_zi
                else:
                    # 创建新记录
                    new_metadata = models.HanziMetadata(
                        unicode_code=unicode_code,
                        is_fan_ti_zi=is_fan_ti_zi,
                        is_jian_ti_zi=is_jian_ti_zi,
                        is_zheng_ti_zi=is_zheng_ti_zi,
                        is_yi_ti_zi=is_yi_ti_zi
                    )
                    db.add(new_metadata)

            # 不在这里commit，让外层方法统一commit
            return True

        except Exception as e:
            print(f"更新metadata时发生错误: {e}")
            return False
    
    @staticmethod
    def update_hanzi_relations(db: Session, unicode_code: str, batch_update: schemas.BatchRelationUpdate) -> bool:
        """更新汉字关系"""
        try:
            # 获取当前汉字的直接关联汉字，构建网络范围
            visited: Set[str] = set()
            all_hanzi_unicodes: List[str] = []
            
            def dfs_relations(current_unicode: str):
                """深度优先搜索关联汉字"""
                if current_unicode in visited:
                    return
                
                visited.add(current_unicode)
                all_hanzi_unicodes.append(current_unicode)
                
                # 获取直接关联的汉字Unicode
                related_unicodes = HanziRelationCRUD.get_related_hanzi_unicodes(db, current_unicode)
                
                # 递归查询每个关联汉字
                for related_unicode in related_unicodes:
                    dfs_relations(related_unicode)
            
            # 从目标汉字开始递归查询
            dfs_relations(unicode_code)
            
            # 获取网络中所有现有关系
            existing_relations = db.query(models.HanziRelation).filter(
                and_(
                    models.HanziRelation.source_unicode.in_(all_hanzi_unicodes),
                    models.HanziRelation.target_unicode.in_(all_hanzi_unicodes)
                )
            ).all()
            
            # 构建新关系列表
            new_relations = []
            for zhengyi_relation in batch_update.zhengyi_relations:
                new_relations.append({
                    'source_unicode': zhengyi_relation.source_unicode,
                    'target_unicode': zhengyi_relation.target_unicode,
                    'relation_type': zhengyi_relation.relation_type,
                    'relation_detail': zhengyi_relation.relation_detail or ''
                })
            
            for fanjian_relation in batch_update.fanjian_relations:
                new_relations.append({
                    'source_unicode': fanjian_relation.source_unicode,
                    'target_unicode': fanjian_relation.target_unicode,
                    'relation_type': fanjian_relation.relation_type,
                    'relation_detail': fanjian_relation.relation_detail or ''
                })
            
            # 创建现有关系的集合（用于快速查找）
            existing_relation_keys = set()
            existing_relation_dict = {}
            for relation in existing_relations:
                key = (relation.source_unicode, relation.target_unicode, relation.relation_type)
                existing_relation_keys.add(key)
                existing_relation_dict[key] = relation
            
            # 创建新关系的集合
            new_relation_keys = set()
            new_relation_dict = {}
            for relation in new_relations:
                key = (relation['source_unicode'], relation['target_unicode'], relation['relation_type'])
                new_relation_keys.add(key)
                new_relation_dict[key] = relation
            
            # 找出需要删除的关系
            relations_to_delete = existing_relation_keys - new_relation_keys
            for key in relations_to_delete:
                relation = existing_relation_dict[key]
                db.delete(relation)
            
            # 找出需要创建的关系
            relations_to_create = new_relation_keys - existing_relation_keys
            for key in relations_to_create:
                relation_data = new_relation_dict[key]
                db_relation = models.HanziRelation(**relation_data)
                db.add(db_relation)
            
            # 找出需要更新的关系
            relations_to_update = new_relation_keys & existing_relation_keys
            for key in relations_to_update:
                existing_relation = existing_relation_dict[key]
                new_relation_data = new_relation_dict[key]
                
                # 更新relation_detail字段
                if existing_relation.relation_detail != new_relation_data['relation_detail']:
                    existing_relation.relation_detail = new_relation_data['relation_detail']
            
            # 收集所有受影响的汉字unicode（包括新关系中的汉字）
            all_affected_unicodes = set(all_hanzi_unicodes)
            for relation in new_relations:
                all_affected_unicodes.add(relation['source_unicode'])
                all_affected_unicodes.add(relation['target_unicode'])
            
            # 刷新会话以确保能看到当前事务中的关系变更
            db.flush()

            # 根据最新的关系更新所有受影响汉字的metadata
            success = RelationNetworkCRUD._update_metadata_from_relations(
                db, list(all_affected_unicodes)
            )
            
            if success:
                # 统一提交所有更新（关系 + metadata）
                db.commit()
                return True
            else:
                db.rollback()
                return False
                
        except Exception as e:
            print(f"批量更新关系时发生错误: {e}")
            db.rollback()
            return False

# 汉字字形信息CRUD操作
class HanziZixingCRUD:

    @staticmethod
    def create_or_update(db: Session, zixing: schemas.HanziZixingCreate) -> models.HanziZixing:
        """创建或更新汉字字形信息"""
        # 查找是否已存在
        existing = db.query(models.HanziZixing).filter(
            models.HanziZixing.unicode_code == zixing.unicode_code
        ).first()

        if existing:
            # 更新现有记录
            for key, value in zixing.model_dump().items():
                setattr(existing, key, value)
            db.commit()
            db.refresh(existing)
            return existing
        else:
            # 创建新记录
            db_zixing = models.HanziZixing(**zixing.model_dump())
            db.add(db_zixing)
            db.commit()
            db.refresh(db_zixing)
            return db_zixing

    @staticmethod
    def get_by_unicode(db: Session, unicode_code: str) -> Optional[models.HanziZixing]:
        """根据Unicode编码获取字形信息"""
        return db.query(models.HanziZixing).filter(
            models.HanziZixing.unicode_code == unicode_code
        ).first()

# 广韵原始数据CRUD操作
class YinyunGyOriginCRUD:

    @staticmethod
    def create(db: Session, gy_origin: schemas.YinyunGyOriginCreate) -> models.YinyunGyOrigin:
        """创建广韵原始数据记录"""
        db_gy_origin = models.YinyunGyOrigin(**gy_origin.model_dump())
        db.add(db_gy_origin)
        db.commit()
        db.refresh(db_gy_origin)
        return db_gy_origin

    @staticmethod
    def get_by_unicode_source(db: Session, unicode: str, source: str) -> List[models.YinyunGyOrigin]:
        """根据Unicode和来源获取广韵数据"""
        return db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.unicode == unicode,
            models.YinyunGyOrigin.source == source
        ).all()

    @staticmethod
    def get_by_unicode(db: Session, unicode: str) -> List[models.YinyunGyOrigin]:
        """根据Unicode获取所有来源的广韵数据"""
        return db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.unicode == unicode
        ).all()

    @staticmethod
    def get_by_unicode_and_refs(db: Session, unicode: str, guangyun_ids: List[int]) -> List[models.YinyunGyOrigin]:
        """
        根据Unicode和guangyun记录ID获取原始数据
        包括：
        1. 当前Unicode的所有记录
        2. ref字段为guangyun_ids中任一ID的记录
        """
        from sqlalchemy import or_

        # 构建查询条件
        conditions = [models.YinyunGyOrigin.unicode == unicode]

        # 如果有guangyun_ids，添加ref条件
        if guangyun_ids:
            conditions.append(models.YinyunGyOrigin.ref.in_(guangyun_ids))

        # 使用OR条件查询
        return db.query(models.YinyunGyOrigin).filter(
            or_(*conditions)
        ).all()

    @staticmethod
    def get_by_id(db: Session, record_id: int) -> Optional[models.YinyunGyOrigin]:
        """根据ID获取广韵原始数据记录"""
        return db.query(models.YinyunGyOrigin).filter(
            models.YinyunGyOrigin.id == record_id
        ).first()

    @staticmethod
    def update(db: Session, record_id: int, gy_origin_update: schemas.YinyunGyOriginUpdate) -> Optional[models.YinyunGyOrigin]:
        """更新广韵原始数据记录"""
        try:
            # 查找记录
            db_gy_origin = db.query(models.YinyunGyOrigin).filter(
                models.YinyunGyOrigin.id == record_id
            ).first()

            if not db_gy_origin:
                return None

            # 保存原始数据用于日志记录
            old_data = {
                'source': db_gy_origin.source,
                'order_num': db_gy_origin.order_num,
                'ref': db_gy_origin.ref,
                'fan_qie': db_gy_origin.fan_qie,
                'sheng_mu': db_gy_origin.sheng_mu,
                'yun_bu': db_gy_origin.yun_bu,
                'sheng_diao': db_gy_origin.sheng_diao,
                'kai_he': db_gy_origin.kai_he,
                'deng_di': db_gy_origin.deng_di,
                'she': db_gy_origin.she,
                'xiao_yun': db_gy_origin.xiao_yun,
                'qing_zhuo': db_gy_origin.qing_zhuo,
                'shi_yi': db_gy_origin.shi_yi
            }

            # 更新字段
            update_data = gy_origin_update.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_gy_origin, key, value)

            db.commit()
            db.refresh(db_gy_origin)

            # 记录变更日志
            try:
                from app.services.log_service import GuangyunLogService

                new_data = {
                    'source': db_gy_origin.source,
                    'order_num': db_gy_origin.order_num,
                    'ref': db_gy_origin.ref,
                    'fan_qie': db_gy_origin.fan_qie,
                    'sheng_mu': db_gy_origin.sheng_mu,
                    'yun_bu': db_gy_origin.yun_bu,
                    'sheng_diao': db_gy_origin.sheng_diao,
                    'kai_he': db_gy_origin.kai_he,
                    'deng_di': db_gy_origin.deng_di,
                    'she': db_gy_origin.she,
                    'xiao_yun': db_gy_origin.xiao_yun,
                    'qing_zhuo': db_gy_origin.qing_zhuo,
                    'shi_yi': db_gy_origin.shi_yi
                }

                # 记录日志
                log_result = GuangyunLogService.log_source_update(
                    db,
                    db_gy_origin.unicode,
                    db_gy_origin.hanzi,
                    record_id,
                    old_data,
                    new_data,
                    db_gy_origin.source
                )

                # 调试信息
                print(f"=== 原始数据更新日志调试 ===")
                print(f"记录ID: {record_id}")
                print(f"Unicode: {db_gy_origin.unicode}")
                print(f"汉字: {db_gy_origin.hanzi}")
                print(f"旧数据: {old_data}")
                print(f"新数据: {new_data}")

                if log_result:
                    print(f"✓ 原始数据更新日志记录成功 - 日志ID: {log_result.id}")
                else:
                    print("✗ 原始数据更新日志未记录 - 可能没有检测到变更")
                print(f"=== 调试结束 ===\n")

            except Exception as log_error:
                # 日志记录失败不影响主要操作
                print(f"记录原始数据日志失败: {str(log_error)}")

            return db_gy_origin
        except Exception:
            db.rollback()
            return None

    @staticmethod
    def delete_by_unicode_source(db: Session, unicode: str, source: str) -> bool:
        """删除指定Unicode和来源的所有记录"""
        try:
            deleted_count = db.query(models.YinyunGyOrigin).filter(
                models.YinyunGyOrigin.unicode == unicode,
                models.YinyunGyOrigin.source == source
            ).delete()
            db.commit()
            return deleted_count > 0
        except Exception:
            db.rollback()
            return False

# 韵书广韵CRUD操作
class YunshuGuangyunCRUD:

    @staticmethod
    def create(db: Session, guangyun: schemas.YunshuGuangyunCreate) -> models.YunshuGuangyun:
        """创建韵书广韵数据记录"""
        db_guangyun = models.YunshuGuangyun(**guangyun.model_dump())
        db.add(db_guangyun)
        db.commit()
        db.refresh(db_guangyun)
        return db_guangyun

    @staticmethod
    def get_by_unicode(db: Session, unicode: str) -> List[models.YunshuGuangyun]:
        """根据Unicode获取韵书广韵数据"""
        return db.query(models.YunshuGuangyun).filter(
            models.YunshuGuangyun.unicode == unicode
        ).all()

    @staticmethod
    def get_by_id(db: Session, record_id: int) -> Optional[models.YunshuGuangyun]:
        """根据ID获取韵书广韵数据记录"""
        return db.query(models.YunshuGuangyun).filter(
            models.YunshuGuangyun.id == record_id
        ).first()

    @staticmethod
    def update(db: Session, record_id: int, guangyun_update: schemas.YunshuGuangyunUpdate) -> Optional[models.YunshuGuangyun]:
        """更新韵书广韵数据记录"""
        try:
            # 查找记录
            db_guangyun = db.query(models.YunshuGuangyun).filter(
                models.YunshuGuangyun.id == record_id
            ).first()

            if not db_guangyun:
                return None

            # 保存原始数据用于日志记录
            old_data = {
                'fan_qie': db_guangyun.fan_qie,
                'sheng_mu': db_guangyun.sheng_mu,
                'yun_bu': db_guangyun.yun_bu,
                'sheng_diao': db_guangyun.sheng_diao,
                'kai_he': db_guangyun.kai_he,
                'deng_di': db_guangyun.deng_di,
                'she': db_guangyun.she,
                'xiao_yun': db_guangyun.xiao_yun,
                'qing_zhuo': db_guangyun.qing_zhuo,
                'shi_yi': db_guangyun.shi_yi,
                'conflicts': db_guangyun.conflicts
            }

            # 更新字段
            update_data = guangyun_update.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_guangyun, key, value)

            db.commit()
            db.refresh(db_guangyun)

            # 记录变更日志
            try:
                from app.services.log_service import GuangyunLogService

                new_data = {
                    'fan_qie': db_guangyun.fan_qie,
                    'sheng_mu': db_guangyun.sheng_mu,
                    'yun_bu': db_guangyun.yun_bu,
                    'sheng_diao': db_guangyun.sheng_diao,
                    'kai_he': db_guangyun.kai_he,
                    'deng_di': db_guangyun.deng_di,
                    'she': db_guangyun.she,
                    'xiao_yun': db_guangyun.xiao_yun,
                    'qing_zhuo': db_guangyun.qing_zhuo,
                    'shi_yi': db_guangyun.shi_yi,
                    'conflicts': db_guangyun.conflicts
                }

                # 检查冲突数量变化
                conflict_change = None
                if old_data.get('conflicts') != new_data.get('conflicts'):
                    conflict_change = {
                        'old_count': old_data.get('conflicts') or 0,
                        'new_count': new_data.get('conflicts') or 0
                    }

                # 记录日志
                GuangyunLogService.log_proofreading_update(
                    db,
                    db_guangyun.unicode,
                    db_guangyun.hanzi,
                    record_id,
                    old_data,
                    new_data,
                    conflict_change
                )
            except Exception as log_error:
                # 日志记录失败不影响主要操作
                print(f"记录日志失败: {str(log_error)}")

            return db_guangyun
        except Exception:
            db.rollback()
            return None

    @staticmethod
    def delete_by_unicode(db: Session, unicode: str) -> bool:
        """删除指定Unicode的所有记录"""
        try:
            deleted_count = db.query(models.YunshuGuangyun).filter(
                models.YunshuGuangyun.unicode == unicode
            ).delete()
            db.commit()
            return deleted_count > 0
        except Exception:
            db.rollback()
            return False

# 校对记录日志CRUD操作
class YunshuCheckLogCRUD:

    @staticmethod
    def create(db: Session, log_data: schemas.YunshuCheckLogCreate) -> models.YunshuCheckLog:
        """创建校对记录日志"""
        db_log = models.YunshuCheckLog(**log_data.model_dump())
        db.add(db_log)
        db.commit()
        db.refresh(db_log)
        return db_log

    @staticmethod
    def get_by_id(db: Session, log_id: int) -> Optional[models.YunshuCheckLog]:
        """根据ID获取校对记录日志"""
        return db.query(models.YunshuCheckLog).filter(
            models.YunshuCheckLog.id == log_id
        ).first()

    @staticmethod
    def get_by_unicode(db: Session, unicode: str, limit: int = 50) -> List[models.YunshuCheckLog]:
        """根据Unicode获取校对记录日志"""
        return db.query(models.YunshuCheckLog).filter(
            models.YunshuCheckLog.unicode == unicode
        ).order_by(models.YunshuCheckLog.create_at.desc()).limit(limit).all()

    @staticmethod
    def get_by_ref_id(db: Session, ref_id: int, limit: int = 20) -> List[models.YunshuCheckLog]:
        """根据关联ID获取校对记录日志"""
        return db.query(models.YunshuCheckLog).filter(
            models.YunshuCheckLog.ref_id == ref_id
        ).order_by(models.YunshuCheckLog.create_at.desc()).limit(limit).all()

    @staticmethod
    def get_by_hanzi(db: Session, hanzi: str, limit: int = 50) -> List[models.YunshuCheckLog]:
        """根据汉字获取校对记录日志"""
        return db.query(models.YunshuCheckLog).filter(
            models.YunshuCheckLog.hanzi == hanzi
        ).order_by(models.YunshuCheckLog.create_at.desc()).limit(limit).all()

    @staticmethod
    def delete_by_id(db: Session, log_id: int) -> bool:
        """删除指定ID的日志记录"""
        try:
            deleted_count = db.query(models.YunshuCheckLog).filter(
                models.YunshuCheckLog.id == log_id
            ).delete()
            db.commit()
            return deleted_count > 0
        except Exception:
            db.rollback()
            return False


# 仪表板CRUD操作
class DashboardCRUD:

    @staticmethod
    def get_overview_stats(db: Session) -> Dict:
        """获取仪表板概览统计数据"""
        from sqlalchemy import func
        from typing import Dict

        # 基础统计
        hanzi_count = db.query(func.count(models.Hanzi.unicode_code)).scalar() or 0
        relation_count = db.query(func.count(models.HanziRelation.id)).scalar() or 0
        guangyun_count = db.query(func.count(models.YunshuGuangyun.id)).scalar() or 0
        origin_count = db.query(func.count(models.YinyunGyOrigin.id)).scalar() or 0

        # 关系类型分布
        zhengyi_count = db.query(func.count(models.HanziRelation.id)).filter(
            models.HanziRelation.relation_type == 'zhengyi'
        ).scalar() or 0
        fanjian_count = db.query(func.count(models.HanziRelation.id)).filter(
            models.HanziRelation.relation_type == 'fanjian'
        ).scalar() or 0

        # 来源分布
        source_stats = db.query(
            models.YinyunGyOrigin.source,
            func.count(models.YinyunGyOrigin.id).label('count')
        ).group_by(models.YinyunGyOrigin.source).all()

        source_distribution = {stat.source: stat.count for stat in source_stats}

        # 冲突统计 - 从冲突记录表获取
        total_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).scalar() or 0
        unresolved_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'unresolved'
        ).scalar() or 0
        resolved_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'resolved'
        ).scalar() or 0
        ignored_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'ignored'
        ).scalar() or 0

        # ref关联统计 - 修复：统计实际有ref值的origin记录
        associated_records = db.query(func.count(models.YinyunGyOrigin.id)).filter(
            models.YinyunGyOrigin.ref.is_not(None)
        ).scalar() or 0
        missing_refs = db.query(func.count(models.YinyunGyOrigin.id)).filter(
            models.YinyunGyOrigin.ref.is_(None)
        ).scalar() or 0
        association_rate = round((associated_records / origin_count * 100), 1) if origin_count > 0 else 0

        # 广韵覆盖的汉字数量
        guangyun_hanzi_count = db.query(func.count(func.distinct(models.YinyunGyOrigin.unicode))).scalar() or 0
        coverage_rate = round((guangyun_hanzi_count / hanzi_count * 100), 1) if hanzi_count > 0 else 0

        # 关系对广韵的影响
        guangyun_impact = min(relation_count * 0.5, guangyun_hanzi_count)  # 估算值

        return {
            "guangyun_core_metrics": {
                "total_progress": {
                    "completed": associated_records,  # 使用已关联的origin记录数作为完成数
                    "total_records": origin_count,
                    "percentage": round((associated_records / origin_count * 100), 1) if origin_count > 0 else 0
                },
                "conflict_resolution": {
                    "total_conflicts": total_conflicts,
                    "resolved_conflicts": resolved_conflicts,
                    "pending_conflicts": unresolved_conflicts,
                    "ignored_conflicts": ignored_conflicts,
                    "resolution_rate": round(((resolved_conflicts + ignored_conflicts) / total_conflicts * 100), 1) if total_conflicts > 0 else 100.0
                },
                "ref_association": {
                    "associated_records": associated_records,
                    "total_records": origin_count,
                    "missing_refs": missing_refs,
                    "association_rate": association_rate
                }
            },
            "supporting_data": {
                "hanzi_foundation": {
                    "total_hanzi": hanzi_count,
                    "guangyun_coverage": guangyun_hanzi_count,
                    "coverage_rate": coverage_rate
                },
                "relation_support": {
                    "zhengyi_relations": zhengyi_count,
                    "fanjian_relations": fanjian_count,
                    "guangyun_impact": int(guangyun_impact)
                },
                "source_distribution": source_distribution
            }
        }



    @staticmethod
    def get_recent_activities(db: Session, limit: int = 10) -> List[Dict]:
        """获取最近的活动记录"""
        from datetime import datetime
        from typing import Dict, List

        # 获取最近的校对记录
        recent_guangyun = db.query(models.YunshuGuangyun).order_by(
            models.YunshuGuangyun.update_at.desc()
        ).limit(limit // 2).all()

        activities = []
        for record in recent_guangyun:
            activities.append({
                "id": record.id,
                "type": "guangyun_update",
                "description": f"更新汉字'{record.hanzi}'的广韵数据",
                "hanzi": record.hanzi,
                "unicode": f"U+{record.unicode}",
                "timestamp": record.update_at or record.create_at,
                "user": "admin"
            })

        # 获取最近的关系记录
        recent_relations = db.query(models.HanziRelation).order_by(
            models.HanziRelation.updated_at.desc()
        ).limit(limit // 2).all()

        for relation in recent_relations:
            source_hanzi = db.query(models.Hanzi).filter(
                models.Hanzi.unicode_code == relation.source_unicode
            ).first()
            target_hanzi = db.query(models.Hanzi).filter(
                models.Hanzi.unicode_code == relation.target_unicode
            ).first()

            if source_hanzi and target_hanzi:
                activities.append({
                    "id": f"rel_{relation.id}",
                    "type": "relation_create",
                    "description": f"创建{relation.relation_type}关系：{source_hanzi.character} -> {target_hanzi.character}",
                    "hanzi": source_hanzi.character,
                    "timestamp": relation.updated_at or relation.created_at,
                    "user": "admin"
                })

        # 按时间排序
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        return activities[:limit]


# 创建CRUD实例
hanzi_crud = HanziCRUD()
relation_crud = HanziRelationCRUD()
metadata_crud = HanziMetadataCRUD()
network_crud = RelationNetworkCRUD()
zixing_crud = HanziZixingCRUD()
gy_origin_crud = YinyunGyOriginCRUD()
yunshu_guangyun_crud = YunshuGuangyunCRUD()
yunshu_check_log_crud = YunshuCheckLogCRUD()
dashboard_crud = DashboardCRUD()


class ConflictRecordCRUD:
    """冲突记录CRUD操作"""

    def create_conflict_record(self, db: Session, conflict_data: schemas.YunshuConflictRecordCreate) -> models.YunshuConflictRecord:
        """创建冲突记录"""
        db_conflict = models.YunshuConflictRecord(**conflict_data.model_dump())
        db.add(db_conflict)
        db.commit()
        db.refresh(db_conflict)
        return db_conflict

    def get_conflict_statistics(self, db: Session) -> schemas.ConflictStatistics:
        """获取冲突统计信息"""
        total_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).scalar() or 0
        unresolved_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'unresolved'
        ).scalar() or 0
        resolved_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'resolved'
        ).scalar() or 0
        ignored_conflicts = db.query(func.count(models.YunshuConflictRecord.id)).filter(
            models.YunshuConflictRecord.conflict_status == 'ignored'
        ).scalar() or 0

        resolution_rate = round(((resolved_conflicts + ignored_conflicts) / total_conflicts * 100), 1) if total_conflicts > 0 else 100.0

        return schemas.ConflictStatistics(
            total_conflicts=total_conflicts,
            unresolved_conflicts=unresolved_conflicts,
            resolved_conflicts=resolved_conflicts,
            ignored_conflicts=ignored_conflicts,
            resolution_rate=resolution_rate
        )

    def get_conflicts_by_field(self, db: Session) -> List[schemas.ConflictByField]:
        """按字段统计冲突"""
        results = db.query(
            models.YunshuConflictRecord.field_name,
            models.YunshuConflictRecord.field_display_name,
            func.count(models.YunshuConflictRecord.id).label('conflict_count'),
            func.sum(
                case(
                    (models.YunshuConflictRecord.conflict_status == 'unresolved', 1),
                    else_=0
                )
            ).label('unresolved_count')
        ).group_by(
            models.YunshuConflictRecord.field_name,
            models.YunshuConflictRecord.field_display_name
        ).order_by(func.count(models.YunshuConflictRecord.id).desc()).all()

        return [
            schemas.ConflictByField(
                field_name=result.field_name,
                field_display_name=result.field_display_name,
                conflict_count=result.conflict_count,
                unresolved_count=result.unresolved_count
            )
            for result in results
        ]

    def update_conflict_status(self, db: Session, conflict_id: int,
                              update_data: schemas.YunshuConflictRecordUpdate) -> Optional[models.YunshuConflictRecord]:
        """更新冲突状态"""
        db_conflict = db.query(models.YunshuConflictRecord).filter(
            models.YunshuConflictRecord.id == conflict_id
        ).first()

        if not db_conflict:
            return None

        update_dict = update_data.model_dump(exclude_unset=True)
        for key, value in update_dict.items():
            setattr(db_conflict, key, value)

        db.commit()
        db.refresh(db_conflict)
        return db_conflict

    def update_conflict(self, db: Session, conflict_id: int,
                       update_data: schemas.YunshuConflictRecordUpdate) -> Optional[models.YunshuConflictRecord]:
        """更新冲突记录（通用方法）"""
        return self.update_conflict_status(db, conflict_id, update_data)

    def get_conflicts_list(self, db: Session, skip: int = 0, limit: int = 100,
                          status: Optional[str] = None, unicode: Optional[str] = None,
                          fan_qie: Optional[str] = None) -> List[models.YunshuConflictRecord]:
        """获取冲突列表"""
        query = db.query(models.YunshuConflictRecord)

        if status:
            query = query.filter(models.YunshuConflictRecord.conflict_status == status)

        if unicode:
            # 处理Unicode格式，移除U+前缀（如果存在）
            clean_unicode = unicode.replace('U+', '').replace('u+', '').upper()
            query = query.filter(models.YunshuConflictRecord.unicode == clean_unicode)

        if fan_qie:
            query = query.filter(models.YunshuConflictRecord.fan_qie == fan_qie)

        return query.order_by(models.YunshuConflictRecord.created_at.desc()).offset(skip).limit(limit).all()


conflict_record_crud = ConflictRecordCRUD()