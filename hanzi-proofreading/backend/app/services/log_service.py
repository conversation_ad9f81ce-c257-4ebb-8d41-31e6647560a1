"""
广韵校对记录日志服务
"""
import json
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from app import schemas, crud, models


class GuangyunLogService:
    """广韵校对记录日志服务类"""

    @staticmethod
    def normalize_value(value):
        """标准化值，将空字符串和None统一处理"""
        if value is None or value == "":
            return None
        return value

    @staticmethod
    def values_are_equal(old_value, new_value):
        """比较两个值是否相等，考虑空值的情况"""
        normalized_old = GuangyunLogService.normalize_value(old_value)
        normalized_new = GuangyunLogService.normalize_value(new_value)
        return normalized_old == normalized_new

    @staticmethod
    def create_log_content(
        operation_type: str,
        table_name: str,
        record_id: int,
        changes: Dict[str, Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """创建日志内容JSON"""
        content = {
            "operation_type": operation_type,
            "table_name": table_name,
            "record_id": record_id,
            "changes": changes,
            "metadata": metadata or {},
            "timestamp": datetime.utcnow().isoformat() + "Z"
        }
        return json.dumps(content, ensure_ascii=False, indent=2)
    
    @staticmethod
    def log_proofreading_update(
        db: Session,
        unicode: str,
        hanzi: str,
        record_id: int,
        old_data: Dict[str, Any],
        new_data: Dict[str, Any],
        conflict_count_change: Optional[Dict[str, int]] = None
    ) -> Optional[models.YunshuCheckLog]:
        """记录校对数据更新日志"""
        try:
            changes = {}
            # 检查需要记录的字段
            fields_to_check = [
                'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao', 
                'kai_he', 'deng_di', 'she', 'xiao_yun', 
                'qing_zhuo', 'shi_yi', 'conflicts'
            ]
            
            for field in fields_to_check:
                old_val = old_data.get(field)
                new_val = new_data.get(field)
                if not GuangyunLogService.values_are_equal(old_val, new_val):
                    changes[field] = {
                        "old_value": GuangyunLogService.normalize_value(old_val),
                        "new_value": GuangyunLogService.normalize_value(new_val)
                    }
            
            # 如果没有变更，不记录日志
            if not changes:
                return None
            
            metadata = {}
            if conflict_count_change:
                metadata["conflict_count_change"] = conflict_count_change
            
            content = GuangyunLogService.create_log_content(
                "update_proofreading",
                "yunshu_guangyun",
                record_id,
                changes,
                metadata
            )
            
            log_data = schemas.YunshuCheckLogCreate(
                unicode=unicode,
                hanzi=hanzi,
                ref_id=record_id,
                content=content
            )
            
            return crud.yunshu_check_log_crud.create(db, log_data)
            
        except Exception as e:
            print(f"记录校对数据更新日志失败: {str(e)}")
            return None
    
    @staticmethod
    def log_source_update(
        db: Session,
        unicode: str,
        hanzi: str,
        record_id: int,
        old_data: Dict[str, Any],
        new_data: Dict[str, Any],
        source_info: Optional[str] = None
    ) -> Optional[models.YunshuCheckLog]:
        """记录原始数据更新日志"""
        try:
            changes = {}
            # 检查需要记录的字段
            fields_to_check = [
                'source', 'order_num', 'ref', 'fan_qie', 'sheng_mu', 
                'yun_bu', 'sheng_diao', 'kai_he', 'deng_di', 'she',
                'xiao_yun', 'qing_zhuo', 'shi_yi'
            ]
            
            print(f"=== 日志服务调试 - log_source_update ===")
            print(f"Unicode: {unicode}, 记录ID: {record_id}")

            for field in fields_to_check:
                old_val = old_data.get(field)
                new_val = new_data.get(field)
                are_equal = GuangyunLogService.values_are_equal(old_val, new_val)
                print(f"字段 {field}: '{old_val}' -> '{new_val}' (相等: {are_equal})")

                if not are_equal:
                    changes[field] = {
                        "old_value": GuangyunLogService.normalize_value(old_val),
                        "new_value": GuangyunLogService.normalize_value(new_val)
                    }

            print(f"检测到的变更: {changes}")

            # 如果没有变更，不记录日志
            if not changes:
                print("没有检测到变更，不记录日志")
                return None
            
            metadata = {}
            if source_info:
                metadata["source_info"] = source_info
            
            content = GuangyunLogService.create_log_content(
                "update_source",
                "yunshu_gy_origin",
                record_id,
                changes,
                metadata
            )
            
            log_data = schemas.YunshuCheckLogCreate(
                unicode=unicode,
                hanzi=hanzi,
                ref_id=record_id,
                content=content
            )
            
            return crud.yunshu_check_log_crud.create(db, log_data)
            
        except Exception as e:
            print(f"记录原始数据更新日志失败: {str(e)}")
            return None
    
    @staticmethod
    def log_relation_create(
        db: Session,
        unicode: str,
        hanzi: str,
        source_record_id: int,
        proofreading_record_id: int
    ) -> Optional[models.YunshuCheckLog]:
        """记录关联关系建立日志"""
        try:
            changes = {
                "relation": {
                    "old_value": None,
                    "new_value": f"source:{source_record_id} -> proofreading:{proofreading_record_id}"
                }
            }
            
            metadata = {
                "source_record_id": source_record_id,
                "proofreading_record_id": proofreading_record_id
            }
            
            content = GuangyunLogService.create_log_content(
                "create_relation",
                "yunshu_gy_origin",
                source_record_id,
                changes,
                metadata
            )
            
            log_data = schemas.YunshuCheckLogCreate(
                unicode=unicode,
                hanzi=hanzi,
                ref_id=source_record_id,
                content=content
            )
            
            return crud.yunshu_check_log_crud.create(db, log_data)
            
        except Exception as e:
            print(f"记录关联关系建立日志失败: {str(e)}")
            return None
    
    @staticmethod
    def log_proofreading_create(
        db: Session,
        unicode: str,
        hanzi: str,
        record_id: int,
        data: Dict[str, Any]
    ) -> Optional[models.YunshuCheckLog]:
        """记录校对数据创建日志"""
        try:
            changes = {
                "new_record": {
                    "old_value": None,
                    "new_value": "创建新的校对记录"
                }
            }
            
            # 记录创建的数据内容
            metadata = {
                "created_data": {
                    field: data.get(field) for field in [
                        'fan_qie', 'sheng_mu', 'yun_bu', 'sheng_diao',
                        'kai_he', 'deng_di', 'she', 'xiao_yun',
                        'qing_zhuo', 'shi_yi', 'conflicts'
                    ] if data.get(field) is not None
                }
            }
            
            content = GuangyunLogService.create_log_content(
                "create_proofreading",
                "yunshu_guangyun",
                record_id,
                changes,
                metadata
            )
            
            log_data = schemas.YunshuCheckLogCreate(
                unicode=unicode,
                hanzi=hanzi,
                ref_id=record_id,
                content=content
            )
            
            return crud.yunshu_check_log_crud.create(db, log_data)
            
        except Exception as e:
            print(f"记录校对数据创建日志失败: {str(e)}")
            return None
    

    
    @staticmethod
    def get_logs_by_hanzi(
        db: Session,
        hanzi: str,
        limit: int = 50
    ) -> List[models.YunshuCheckLog]:
        """根据汉字获取日志记录"""
        try:
            unicode_code = f"{ord(hanzi):04X}"
            return crud.yunshu_check_log_crud.get_by_unicode(db, unicode_code, limit)
        except Exception as e:
            print(f"获取汉字日志记录失败: {str(e)}")
            return []
    
    @staticmethod
    def get_logs_by_record_id(
        db: Session,
        record_id: int,
        limit: int = 20
    ) -> List[models.YunshuCheckLog]:
        """根据记录ID获取日志记录"""
        try:
            return crud.yunshu_check_log_crud.get_by_ref_id(db, record_id, limit)
        except Exception as e:
            print(f"获取记录日志失败: {str(e)}")
            return []
